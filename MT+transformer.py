import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from datasets import load_dataset
import numpy as np
from collections import Counter
import pickle
import math
import random
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

class TranslationDataset(Dataset):
    def __init__(self, english_texts, persian_texts, en_vocab, fa_vocab, max_length=128):
        self.english_texts = english_texts
        self.persian_texts = persian_texts
        self.en_vocab = en_vocab
        self.fa_vocab = fa_vocab
        self.max_length = max_length
        
        # ایجاد دیکشنری برای تبدیل کلمات به ID
        self.en_word2id = {word: idx for idx, word in enumerate(en_vocab)}
        self.fa_word2id = {word: idx for idx, word in enumerate(fa_vocab)}
        
        # اضافه کردن توکن‌های خاص
        self.en_word2id['<PAD>'] = len(self.en_word2id)
        self.en_word2id['<UNK>'] = len(self.en_word2id)
        self.en_word2id['<SOS>'] = len(self.en_word2id)
        self.en_word2id['<EOS>'] = len(self.en_word2id)
        
        self.fa_word2id['<PAD>'] = len(self.fa_word2id)
        self.fa_word2id['<UNK>'] = len(self.fa_word2id)
        self.fa_word2id['<SOS>'] = len(self.fa_word2id)
        self.fa_word2id['<EOS>'] = len(self.fa_word2id)
        
        # دیکشنری معکوس
        self.en_id2word = {idx: word for word, idx in self.en_word2id.items()}
        self.fa_id2word = {idx: word for word, idx in self.fa_word2id.items()}
    
    def tokenize(self, text):
        """توکن کردن متن"""
        return text.lower().split()
    
    def encode_sentence(self, sentence, vocab_dict, add_special_tokens=True):
        """تبدیل جمله به لیست ID ها"""
        tokens = self.tokenize(sentence)
        ids = []
        
        if add_special_tokens:
            ids.append(vocab_dict['<SOS>'])
        
        for token in tokens[:self.max_length-2]:  # جا برای SOS و EOS
            ids.append(vocab_dict.get(token, vocab_dict['<UNK>']))
        
        if add_special_tokens:
            ids.append(vocab_dict['<EOS>'])
        
        # Padding
        while len(ids) < self.max_length:
            ids.append(vocab_dict['<PAD>'])
            
        return ids[:self.max_length]
    
    def __len__(self):
        return len(self.english_texts)
    
    def __getitem__(self, idx):
        en_text = self.english_texts[idx]
        fa_text = self.persian_texts[idx]
        
        en_ids = self.encode_sentence(en_text, self.en_word2id)
        fa_ids = self.encode_sentence(fa_text, self.fa_word2id)
        
        return {
            'english': torch.tensor(en_ids, dtype=torch.long),
            'persian': torch.tensor(fa_ids, dtype=torch.long)
        }

class MultiHeadAttention(nn.Module):
    def __init__(self, d_model, num_heads):
        super().__init__()
        assert d_model % num_heads == 0
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads
        
        self.W_q = nn.Linear(d_model, d_model)
        self.W_k = nn.Linear(d_model, d_model)
        self.W_v = nn.Linear(d_model, d_model)
        self.W_o = nn.Linear(d_model, d_model)
        
    def scaled_dot_product_attention(self, Q, K, V, mask=None):
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.d_k)
        
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
            
        attention_weights = torch.softmax(scores, dim=-1)
        output = torch.matmul(attention_weights, V)
        
        return output, attention_weights
    
    def forward(self, query, key, value, mask=None):
        batch_size = query.size(0)
        
        # تبدیل به Multi-Head
        Q = self.W_q(query).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        K = self.W_k(key).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        V = self.W_v(value).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        
        # محاسبه Attention
        attention_output, attention_weights = self.scaled_dot_product_attention(Q, K, V, mask)
        
        # ترکیب heads
        attention_output = attention_output.transpose(1, 2).contiguous().view(
            batch_size, -1, self.d_model
        )
        
        output = self.W_o(attention_output)
        return output

class PositionalEncoding(nn.Module):
    def __init__(self, d_model, max_length=5000):
        super().__init__()
        
        pe = torch.zeros(max_length, d_model)
        position = torch.arange(0, max_length, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        return x + self.pe[:x.size(0), :].transpose(0, 1)

class TransformerBlock(nn.Module):
    def __init__(self, d_model, num_heads, d_ff, dropout=0.1):
        super().__init__()
        
        self.attention = MultiHeadAttention(d_model, num_heads)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        self.feed_forward = nn.Sequential(
            nn.Linear(d_model, d_ff),
            nn.ReLU(),
            nn.Linear(d_ff, d_model)
        )
        
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x, mask=None):
        # Self-attention
        attn_output = self.attention(x, x, x, mask)
        x = self.norm1(x + self.dropout(attn_output))
        
        # Feed-forward
        ff_output = self.feed_forward(x)
        x = self.norm2(x + self.dropout(ff_output))
        
        return x

class Transformer(nn.Module):
    def __init__(self, src_vocab_size, tgt_vocab_size, d_model=512, num_heads=8, 
                 num_layers=6, d_ff=2048, max_length=128, dropout=0.1):
        super().__init__()
        
        self.d_model = d_model
        self.src_vocab_size = src_vocab_size
        self.tgt_vocab_size = tgt_vocab_size
        
        # Embedding layers
        self.src_embedding = nn.Embedding(src_vocab_size, d_model)
        self.tgt_embedding = nn.Embedding(tgt_vocab_size, d_model)
        
        # Positional encoding
        self.pos_encoding = PositionalEncoding(d_model, max_length)
        
        # Encoder layers
        self.encoder_layers = nn.ModuleList([
            TransformerBlock(d_model, num_heads, d_ff, dropout) 
            for _ in range(num_layers)
        ])
        
        # Decoder layers
        self.decoder_layers = nn.ModuleList([
            TransformerBlock(d_model, num_heads, d_ff, dropout) 
            for _ in range(num_layers)
        ])
        
        # Output projection
        self.output_projection = nn.Linear(d_model, tgt_vocab_size)
        self.dropout = nn.Dropout(dropout)
        
    def create_padding_mask(self, seq, pad_idx=0):
        return (seq != pad_idx).unsqueeze(1).unsqueeze(2)
    
    def create_look_ahead_mask(self, size):
        mask = torch.triu(torch.ones(size, size), diagonal=1)
        return mask == 0
    
    def encode(self, src, src_mask=None):
        # Embedding + Positional encoding
        src_emb = self.src_embedding(src) * math.sqrt(self.d_model)
        src_emb = self.pos_encoding(src_emb)
        src_emb = self.dropout(src_emb)
        
        # Encoder layers
        for layer in self.encoder_layers:
            src_emb = layer(src_emb, src_mask)
            
        return src_emb
    
    def decode(self, tgt, encoder_output, tgt_mask=None, src_mask=None):
        # Embedding + Positional encoding
        tgt_emb = self.tgt_embedding(tgt) * math.sqrt(self.d_model)
        tgt_emb = self.pos_encoding(tgt_emb)
        tgt_emb = self.dropout(tgt_emb)
        
        # Decoder layers
        for layer in self.decoder_layers:
            tgt_emb = layer(tgt_emb, tgt_mask)
            
        return tgt_emb
    
    def forward(self, src, tgt):
        # ایجاد mask ها
        src_mask = self.create_padding_mask(src)
        tgt_mask = self.create_padding_mask(tgt)
        look_ahead_mask = self.create_look_ahead_mask(tgt.size(1))
        tgt_mask = tgt_mask & look_ahead_mask.to(tgt.device)
        
        # Encoding
        encoder_output = self.encode(src, src_mask)
        
        # Decoding
        decoder_output = self.decode(tgt, encoder_output, tgt_mask, src_mask)
        
        # Output projection
        output = self.output_projection(decoder_output)
        
        return output

class BilingualTranslator:
    def __init__(self, model_config=None):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model_config = model_config or {
            'd_model': 512,
            'num_heads': 8,
            'num_layers': 6,
            'd_ff': 2048,
            'max_length': 128,
            'dropout': 0.1
        }
        self.models = {}  # برای ذخیره دو مدل (fa2en و en2fa)
        
    def prepare_data(self, min_freq=2):
        """آماده‌سازی داده‌ها و ایجاد vocabulary"""
        print("در حال بارگیری دیتاست...")
        ds = load_dataset("shenasa/English-Persian-Parallel-Dataset")
        
        # استخراج متن‌های انگلیسی و فارسی
        train_data = ds['train']
        english_texts = [item['en'] for item in train_data if item['en'] and item['fa']]
        persian_texts = [item['fa'] for item in train_data if item['en'] and item['fa']]
        
        print(f"تعداد جملات: {len(english_texts)}")
        
        # ایجاد vocabulary
        en_words = []
        fa_words = []
        
        for text in english_texts:
            en_words.extend(text.lower().split())
            
        for text in persian_texts:
            fa_words.extend(text.split())
        
        # حذف کلمات کم‌فرکانس
        en_counter = Counter(en_words)
        fa_counter = Counter(fa_words)
        
        self.en_vocab = [word for word, freq in en_counter.items() if freq >= min_freq]
        self.fa_vocab = [word for word, freq in fa_counter.items() if freq >= min_freq]
        
        print(f"اندازه vocabulary انگلیسی: {len(self.en_vocab)}")
        print(f"اندازه vocabulary فارسی: {len(self.fa_vocab)}")
        
        # تقسیم داده‌ها
        split_idx = int(0.9 * len(english_texts))
        
        self.train_en = english_texts[:split_idx]
        self.train_fa = persian_texts[:split_idx]
        self.val_en = english_texts[split_idx:]
        self.val_fa = persian_texts[split_idx:]
        
        return self.train_en, self.train_fa, self.val_en, self.val_fa
    
    def create_datasets(self):
        """ایجاد DataLoader ها"""
        # Dataset برای ترجمه فارسی به انگلیسی
        train_dataset_fa2en = TranslationDataset(
            self.train_fa, self.train_en, self.fa_vocab, self.en_vocab
        )
        val_dataset_fa2en = TranslationDataset(
            self.val_fa, self.val_en, self.fa_vocab, self.en_vocab
        )
        
        # Dataset برای ترجمه انگلیسی به فارسی
        train_dataset_en2fa = TranslationDataset(
            self.train_en, self.train_fa, self.en_vocab, self.fa_vocab
        )
        val_dataset_en2fa = TranslationDataset(
            self.val_en, self.val_fa, self.en_vocab, self.fa_vocab
        )
        
        self.train_loader_fa2en = DataLoader(train_dataset_fa2en, batch_size=32, shuffle=True)
        self.val_loader_fa2en = DataLoader(val_dataset_fa2en, batch_size=32, shuffle=False)
        
        self.train_loader_en2fa = DataLoader(train_dataset_en2fa, batch_size=32, shuffle=True)
        self.val_loader_en2fa = DataLoader(val_dataset_en2fa, batch_size=32, shuffle=False)
        
        # ذخیره dataset objects برای استفاده در ترجمه
        self.dataset_fa2en = train_dataset_fa2en
        self.dataset_en2fa = train_dataset_en2fa
    
    def create_models(self):
        """ایجاد مدل‌های Transformer"""
        # مدل برای ترجمه فارسی به انگلیسی
        self.models['fa2en'] = Transformer(
            src_vocab_size=len(self.fa_vocab) + 4,  # +4 برای توکن‌های خاص
            tgt_vocab_size=len(self.en_vocab) + 4,
            **self.model_config
        ).to(self.device)
        
        # مدل برای ترجمه انگلیسی به فارسی
        self.models['en2fa'] = Transformer(
            src_vocab_size=len(self.en_vocab) + 4,
            tgt_vocab_size=len(self.fa_vocab) + 4,
            **self.model_config
        ).to(self.device)
        
        print(f"مدل‌ها روی {self.device} ایجاد شدند")
    
    def train_model(self, direction='fa2en', epochs=10, lr=0.0001):
        """آموزش مدل در جهت مشخص شده"""
        model = self.models[direction]
        train_loader = self.train_loader_fa2en if direction == 'fa2en' else self.train_loader_en2fa
        val_loader = self.val_loader_fa2en if direction == 'fa2en' else self.val_loader_en2fa
        
        optimizer = optim.Adam(model.parameters(), lr=lr)
        criterion = nn.CrossEntropyLoss(ignore_index=0)  # PAD token index
        
        print(f"شروع آموزش مدل {direction}...")
        
        for epoch in range(epochs):
            # Training
            model.train()
            train_loss = 0
            train_steps = 0
            
            for batch in tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs}"):
                if direction == 'fa2en':
                    src = batch['persian'].to(self.device)
                    tgt = batch['english'].to(self.device)
                else:
                    src = batch['english'].to(self.device)
                    tgt = batch['persian'].to(self.device)
                
                # ایجاد input و target برای decoder
                tgt_input = tgt[:, :-1]  # بدون آخرین توکن
                tgt_output = tgt[:, 1:]   # بدون اولین توکن
                
                optimizer.zero_grad()
                
                # Forward pass
                outputs = model(src, tgt_input)
                
                # محاسبه loss
                loss = criterion(
                    outputs.reshape(-1, outputs.size(-1)),
                    tgt_output.reshape(-1)
                )
                
                # Backward pass
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
                train_steps += 1
            
            avg_train_loss = train_loss / train_steps
            
            # Validation
            model.eval()
            val_loss = 0
            val_steps = 0
            
            with torch.no_grad():
                for batch in val_loader:
                    if direction == 'fa2en':
                        src = batch['persian'].to(self.device)
                        tgt = batch['english'].to(self.device)
                    else:
                        src = batch['english'].to(self.device)
                        tgt = batch['persian'].to(self.device)
                    
                    tgt_input = tgt[:, :-1]
                    tgt_output = tgt[:, 1:]
                    
                    outputs = model(src, tgt_input)
                    loss = criterion(
                        outputs.reshape(-1, outputs.size(-1)),
                        tgt_output.reshape(-1)
                    )
                    
                    val_loss += loss.item()
                    val_steps += 1
            
            avg_val_loss = val_loss / val_steps
            print(f"Epoch {epoch+1}: Train Loss = {avg_train_loss:.4f}, Val Loss = {avg_val_loss:.4f}")
    
    def translate(self, text, direction='fa2en', max_length=128):
        """ترجمه متن"""
        model = self.models[direction]
        model.eval()
        
        if direction == 'fa2en':
            dataset = self.dataset_fa2en
            src_vocab = dataset.fa_word2id
            tgt_vocab = dataset.en_word2id
            tgt_id2word = dataset.en_id2word
        else:
            dataset = self.dataset_en2fa
            src_vocab = dataset.en_word2id
            tgt_vocab = dataset.fa_word2id
            tgt_id2word = dataset.fa_id2word
        
        # کدگذاری جمله منبع
        src_ids = dataset.encode_sentence(text, src_vocab, add_special_tokens=True)
        src_tensor = torch.tensor([src_ids], dtype=torch.long).to(self.device)
        
        # ایجاد جمله هدف به تدریج
        tgt_ids = [tgt_vocab['<SOS>']]
        
        with torch.no_grad():
            for _ in range(max_length):
                tgt_tensor = torch.tensor([tgt_ids], dtype=torch.long).to(self.device)
                
                # پیش‌بینی
                output = model(src_tensor, tgt_tensor)
                
                # انتخاب بهترین کلمه
                next_word_logits = output[0, -1, :]
                next_word_id = torch.argmax(next_word_logits).item()
                
                tgt_ids.append(next_word_id)
                
                # اگر به <EOS> رسیدیم، متوقف شویم
                if next_word_id == tgt_vocab['<EOS>']:
                    break
        
        # تبدیل ID ها به کلمات
        translated_words = []
        for word_id in tgt_ids[1:-1]:  # بدون <SOS> و <EOS>
            word = tgt_id2word.get(word_id, '<UNK>')
            if word not in ['<PAD>', '<UNK>']:
                translated_words.append(word)
        
        return ' '.join(translated_words)
    
    def save_models(self, save_path='translator_models.pth'):
        """ذخیره مدل‌ها"""
        torch.save({
            'fa2en_model': self.models['fa2en'].state_dict(),
            'en2fa_model': self.models['en2fa'].state_dict(),
            'en_vocab': self.en_vocab,
            'fa_vocab': self.fa_vocab,
            'model_config': self.model_config
        }, save_path)
        print(f"مدل‌ها در {save_path} ذخیره شدند")
    
    def load_models(self, load_path='translator_models.pth'):
        """بارگیری مدل‌ها"""
        checkpoint = torch.load(load_path, map_location=self.device)
        
        self.en_vocab = checkpoint['en_vocab']
        self.fa_vocab = checkpoint['fa_vocab']
        self.model_config = checkpoint['model_config']
        
        self.create_models()
        
        self.models['fa2en'].load_state_dict(checkpoint['fa2en_model'])
        self.models['en2fa'].load_state_dict(checkpoint['en2fa_model'])
        
        # ایجاد dataset objects برای ترجمه
        dummy_texts = ['dummy'] * 10
        self.dataset_fa2en = TranslationDataset(dummy_texts, dummy_texts, self.fa_vocab, self.en_vocab)
        self.dataset_en2fa = TranslationDataset(dummy_texts, dummy_texts, self.en_vocab, self.fa_vocab)
        
        print(f"مدل‌ها از {load_path} بارگیری شدند")

# مثال استفاده
if __name__ == "__main__":
    # ایجاد ترجمه‌کننده
    translator = BilingualTranslator()
    
    # آماده‌سازی داده‌ها
    translator.prepare_data()
    translator.create_datasets()
    translator.create_models()
    
    print("شروع آموزش مدل فارسی به انگلیسی...")
    translator.train_model('fa2en', epochs=5)
    
    print("شروع آموزش مدل انگلیسی به فارسی...")
    translator.train_model('en2fa', epochs=5)
    
    # ذخیره مدل‌ها
    translator.save_models()
    
    # تست ترجمه
    print("\n=== تست ترجمه ===")
    
    # ترجمه فارسی به انگلیسی
    persian_text = "سلام دوست من"
    english_translation = translator.translate(persian_text, 'fa2en')
    print(f"فارسی: {persian_text}")
    print(f"انگلیسی: {english_translation}")
    
    # ترجمه انگلیسی به فارسی
    english_text = "Hello my friend"
    persian_translation = translator.translate(english_text, 'en2fa')
    print(f"انگلیسی: {english_text}")
    print(f"فارسی: {persian_translation}")